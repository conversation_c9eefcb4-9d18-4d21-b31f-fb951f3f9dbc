# Define version mappings
$versions = @{
    "minecraft-1.21" = "mc1.21.1-0.6.13-fabric"
    "minecraft-1.21.5" = "mc1.21.5-0.6.13-fabric"
    "minecraft-1.21.6" = "mc1.21.6-0.6.13-fabric"
    "minecraft-1.21.7" = "mc1.21.6-0.6.13-fabric"
    "minecraft-1.21.8" = "mc1.21.6-0.6.13-fabric"
    "minecraft-1.20.4" = "mc1.20.4-0.5.8-fabric"
    "minecraft-1.20.1" = "mc1.20.1-0.5.13-fabric"
}

foreach ($version in $versions.Keys) {
    Write-Host "Fixing $version..."
    
    # Fix gradle.properties
    $gradlePropsPath = "$version\gradle.properties"
    $content = Get-Content $gradlePropsPath
    
    for ($i = 0; $i -lt $content.Length; $i++) {
        if ($content[$i] -match "^sodium_version=") {
            $content[$i] = "sodium_version=$($versions[$version])"
        }
    }
    
    Set-Content $gradlePropsPath $content
    
    # Fix build.gradle
    $buildGradlePath = "$version\build.gradle"
    $content = Get-Content $buildGradlePath -Raw
    $content = $content -replace 'modImplementation "maven\.modrinth:sodium:mc\$\{project\.sodium_minecraft_version\}-\$\{project\.sodium_version\}"', 'modImplementation "maven.modrinth:sodium:${project.sodium_version}"'
    Set-Content $buildGradlePath $content
}

Write-Host "Done fixing all Sodium versions!"
