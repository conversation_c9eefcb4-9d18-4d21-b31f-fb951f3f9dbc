# Indium Multi-Version Project

Этот проект содержит адаптированные версии мода Indium для различных версий Minecraft.

## Структура проекта

```
├── minecraft-1.21/     # Minecraft 1.21
├── minecraft-1.21.1/   # Minecraft 1.21.1
├── minecraft-1.21.4/   # Minecraft 1.21.4
├── minecraft-1.21.5/   # Minecraft 1.21.5
├── minecraft-1.21.6/   # Minecraft 1.21.6
├── minecraft-1.21.7/   # Minecraft 1.21.7
├── minecraft-1.21.8/   # Minecraft 1.21.8
├── minecraft-1.20.4/   # Minecraft 1.20.4
└── minecraft-1.20.1/   # Minecraft 1.20.1
```

## Поддерживаемые версии

### Minecraft 1.21.x
- **1.21** - Базовая версия
- **1.21.1** - Fabric API 0.102.0+1.21.1, Sodium 0.5.11
- **1.21.4** - Fabric API 0.110.0+1.21.4, Sodium 0.6.0
- **1.21.5** - Fabric API 0.110.0+1.21.5, Sodium 0.6.0
- **1.21.6** - Fabric API 0.110.0+1.21.6, Sodium 0.6.0
- **1.21.7** - Fabric API 0.110.0+1.21.7, Sodium 0.6.0
- **1.21.8** - Fabric API 0.110.0+1.21.8, Sodium 0.6.0

### Minecraft 1.20.x
- **1.20.4** - Fabric API 0.91.0+1.20.4, Sodium 0.5.8, Java 17
- **1.20.1** - Fabric API 0.83.0+1.20.1, Sodium 0.5.0, Java 17

## Сборка

### Сборка всех версий
```bash
./gradlew buildAll
```

### Сборка конкретной версии
```bash
./gradlew :minecraft-1.21.1:build
```

### Очистка всех версий
```bash
./gradlew cleanAll
```

## Требования

- **Java 21** для версий Minecraft 1.21.x
- **Java 17** для версий Minecraft 1.20.x
- Gradle 8.0+

## Зависимости

Каждая версия имеет свои специфические зависимости:

- **Fabric Loader** - соответствующая версия для каждой версии MC
- **Yarn Mappings** - официальные маппинги для каждой версии
- **Fabric API** - совместимая версия для каждой версии MC
- **Sodium** - совместимая версия для каждой версии MC

## Примечания

- Версии 1.20.x могут требовать дополнительных изменений в коде для полной совместимости
- Версии 1.21.4+ используют более новые версии Fabric Loader и API
- Все версии настроены для работы с соответствующими версиями Sodium

## Использование

1. Выберите нужную папку версии
2. Соберите проект: `./gradlew :minecraft-X.XX:build`
3. Готовый мод будет в папке `minecraft-X.XX/build/libs/`
