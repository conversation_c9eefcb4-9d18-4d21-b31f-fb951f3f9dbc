@echo off
echo Cleaning all versions for NoInteract-only build...

set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo Cleaning %%v...
    
    REM Delete renderer-related files
    rmdir /s /q "%%v\src\main\java\link\infra\indium\renderer" 2>nul
    rmdir /s /q "%%v\src\main\java\link\infra\indium\mixin\renderer" 2>nul
    rmdir /s /q "%%v\src\main\java\link\infra\indium\mixin\sodium" 2>nul
    
    REM Delete other unnecessary files
    del "%%v\src\main\java\link\infra\indium\Indium.java" 2>nul
    del "%%v\src\main\java\link\infra\indium\IndiumRenderer.java" 2>nul
    
    REM Delete Fabric API stubs if they exist
    rmdir /s /q "%%v\src\main\java\net" 2>nul
    
    REM Update mixins.json to only include MixinMinecraftClient
    echo { > "%%v\src\main\resources\indium.mixins.json"
    echo   "required": true, >> "%%v\src\main\resources\indium.mixins.json"
    echo   "package": "link.infra.indium.mixin", >> "%%v\src\main\resources\indium.mixins.json"
    echo   "compatibilityLevel": "JAVA_17", >> "%%v\src\main\resources\indium.mixins.json"
    echo   "client": [ >> "%%v\src\main\resources\indium.mixins.json"
    echo     "MixinMinecraftClient", >> "%%v\src\main\resources\indium.mixins.json"
    echo     "MixinBlockRenderManager", >> "%%v\src\main\resources\indium.mixins.json"
    echo     "MixinEntityRenderDispatcher", >> "%%v\src\main\resources\indium.mixins.json"
    echo     "MixinWorldRenderer", >> "%%v\src\main\resources\indium.mixins.json"
    echo     "MixinEntity" >> "%%v\src\main\resources\indium.mixins.json"
    echo   ], >> "%%v\src\main\resources\indium.mixins.json"
    echo   "injectors": { >> "%%v\src\main\resources\indium.mixins.json"
    echo     "defaultRequire": 1 >> "%%v\src\main\resources\indium.mixins.json"
    echo   } >> "%%v\src\main\resources\indium.mixins.json"
    echo } >> "%%v\src\main\resources\indium.mixins.json"
    
    echo %%v cleaned!
)

echo All versions cleaned for NoInteract-only build!
pause
