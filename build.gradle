// Root build.gradle for Indium Multi-Version Project

plugins {
    id 'fabric-loom' version '1.6.+' apply false
}

allprojects {
    group = 'link.infra'
    version = '1.0.35'
}

subprojects {
    apply plugin: 'fabric-loom'
    apply plugin: 'maven-publish'

    repositories {
        maven {
            url = "https://api.modrinth.com/maven"
        }
    }

    java {
        withSourcesJar()
    }

    jar {
        from "LICENSE"
    }

    processResources {
        inputs.property "version", project.version

        filesMatching("fabric.mod.json") {
            expand "version": project.version
        }
    }

    tasks.withType(JavaCompile).configureEach {
        it.options.encoding = "UTF-8"
    }
}

// Task to build all versions
task buildAll {
    dependsOn subprojects.collect { it.tasks.build }
    description = 'Builds all Minecraft versions'
    group = 'build'
}

// Task to clean all versions
task cleanAll {
    dependsOn subprojects.collect { it.tasks.clean }
    description = 'Cleans all Minecraft versions'
    group = 'build'
}



