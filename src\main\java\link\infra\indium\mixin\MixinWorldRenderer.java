package link.infra.indium.mixin;

import link.infra.indium.other.RenderDistanceEnhancer;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.WorldRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(WorldRenderer.class)
public class MixinWorldRenderer {

    @Inject(method = "renderEntity", at = @At("HEAD"), cancellable = true)
    private void onRenderEntity(Entity entity, double cameraX, double cameraY, double cameraZ, float tickDelta, 
                               MatrixStack matrices, VertexConsumerProvider vertexConsumers, CallbackInfo ci) {
        
        if (!RenderDistanceEnhancer.isEnhancedMode()) return;
        

        
        // скрываем предметы-энтити и декоративные энтити
        EntityType<?> type = entity.getType();
        if (type == EntityType.ITEM ||
            type == EntityType.EXPERIENCE_ORB ||
            type == EntityType.ARROW ||
            type == EntityType.SPECTRAL_ARROW ||
            type == EntityType.TRIDENT ||
            type == EntityType.SNOWBALL ||
            type == EntityType.EGG ||
            type == EntityType.ENDER_PEARL ||
            type == EntityType.EYE_OF_ENDER ||
            type == EntityType.POTION ||
            type == EntityType.EXPERIENCE_BOTTLE ||
            type == EntityType.FIREWORK_ROCKET ||
            type == EntityType.FIREBALL ||
            type == EntityType.SMALL_FIREBALL ||
            type == EntityType.DRAGON_FIREBALL ||
            type == EntityType.WITHER_SKULL ||
            type == EntityType.SHULKER_BULLET ||
            type == EntityType.FALLING_BLOCK ||
            type == EntityType.ITEM_FRAME ||
            type == EntityType.GLOW_ITEM_FRAME ||
            type == EntityType.PAINTING ||
            type == EntityType.BOAT ||
            type == EntityType.CHEST_BOAT ||
            type == EntityType.MINECART ||
            type == EntityType.CHEST_MINECART ||
            type == EntityType.FURNACE_MINECART ||
            type == EntityType.TNT_MINECART ||
            type == EntityType.HOPPER_MINECART ||
            type == EntityType.COMMAND_BLOCK_MINECART ||
            type == EntityType.SPAWNER_MINECART ||
            type == EntityType.END_CRYSTAL ||
            type == EntityType.ARMOR_STAND ||
            type == EntityType.LEASH_KNOT ||
            type == EntityType.AREA_EFFECT_CLOUD ||
            type == EntityType.EVOKER_FANGS ||
            type == EntityType.LIGHTNING_BOLT ||
            type == EntityType.MARKER ||
            type == EntityType.BLOCK_DISPLAY ||
            type == EntityType.ITEM_DISPLAY ||
            type == EntityType.TEXT_DISPLAY ||
            type == EntityType.INTERACTION) {
            
            ci.cancel(); // не рендерим энтити
        }
    }
} 