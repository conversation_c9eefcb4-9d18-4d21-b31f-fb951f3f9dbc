@echo off
echo Copying all classes from 1.21.4 to all versions...

set SOURCE_DIR=minecraft-1.21.4
set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo Copying to %%v...
    
    REM Delete old src directory
    rmdir /s /q "%%v\src" 2>nul
    
    REM Copy entire src directory from 1.21.4
    xcopy "%SOURCE_DIR%\src" "%%v\src" /E /I /Y >nul
    
    REM Copy build.gradle
    copy "%SOURCE_DIR%\build.gradle" "%%v\build.gradle" >nul
    
    REM Copy gradle.properties
    copy "%SOURCE_DIR%\gradle.properties" "%%v\gradle.properties" >nul
    
    REM Copy gradle wrapper
    copy "%SOURCE_DIR%\gradle\wrapper\gradle-wrapper.properties" "%%v\gradle\wrapper\gradle-wrapper.properties" >nul
    copy "%SOURCE_DIR%\gradle\wrapper\gradle-wrapper.jar" "%%v\gradle\wrapper\gradle-wrapper.jar" >nul
    
    REM Copy gradlew files
    copy "%SOURCE_DIR%\gradlew" "%%v\gradlew" >nul
    copy "%SOURCE_DIR%\gradlew.bat" "%%v\gradlew.bat" >nul
    
    echo %%v updated with 1.21.4 code!
)

echo All versions updated with 1.21.4 code!
pause
