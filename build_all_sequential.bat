@echo off
echo Building all versions sequentially...

set VERSIONS=minecraft-1.21 minecraft-1.21.1 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo.
    echo ========================================
    echo Building %%v...
    echo ========================================
    
    cd /d "%%v"
    call gradlew.bat clean build --no-daemon --console=plain
    
    if %ERRORLEVEL% EQU 0 (
        echo %%v - BUILD SUCCESSFUL!
        echo JAR files:
        dir "build\libs\*.jar" /b 2>nul
    ) else (
        echo %%v - BUILD FAILED!
    )
    
    cd ..
    echo.
)

echo.
echo ========================================
echo All builds completed!
echo ========================================

echo.
echo Summary of built JAR files:
for %%v in (minecraft-1.20.1 minecraft-1.20.4 %VERSIONS% minecraft-1.21.4) do (
    if exist "%%v\build\libs\*.jar" (
        echo %%v:
        dir "%%v\build\libs\*.jar" /b 2>nul
        echo.
    )
)

pause
