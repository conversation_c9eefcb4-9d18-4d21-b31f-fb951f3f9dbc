@echo off
echo Building all versions...

set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.4 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo.
    echo ========================================
    echo Building %%v...
    echo ========================================
    
    cd /d "%%v"
    call gradlew.bat build --no-daemon
    
    if %ERRORLEVEL% EQU 0 (
        echo %%v - BUILD SUCCESSFUL!
    ) else (
        echo %%v - BUILD FAILED!
    )
    
    cd ..
)

echo.
echo ========================================
echo All builds completed!
echo ========================================

REM Show all built jars
echo.
echo Built JAR files:
for %%v in (%VERSIONS%) do (
    if exist "%%v\build\libs\*.jar" (
        echo %%v:
        dir "%%v\build\libs\*.jar" /b
        echo.
    )
)

pause
