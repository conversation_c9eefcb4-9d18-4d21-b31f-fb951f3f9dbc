@echo off
echo Updating all versions with 1.21.4 logic...

set SOURCE_DIR=minecraft-1.21.4
set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo Updating %%v...
    
    REM Copy MixinMinecraftClient
    copy "%SOURCE_DIR%\src\main\java\link\infra\indium\mixin\MixinMinecraftClient.java" "%%v\src\main\java\link\infra\indium\mixin\MixinMinecraftClient.java" >nul
    
    REM Copy RenderDistanceEnhancer
    copy "%SOURCE_DIR%\src\main\java\link\infra\indium\other\RenderDistanceEnhancer.java" "%%v\src\main\java\link\infra\indium\other\RenderDistanceEnhancer.java" >nul
    
    REM Copy PerformanceEnhancer
    copy "%SOURCE_DIR%\src\main\java\link\infra\indium\PerformanceEnhancer.java" "%%v\src\main\java\link\infra\indium\PerformanceEnhancer.java" >nul
    
    REM Update fabric.mod.json entrypoint
    powershell -Command "(Get-Content '%%v\src\main\resources\fabric.mod.json') -replace 'link\.infra\.indium\.Indium', 'link.infra.indium.PerformanceEnhancer' | Set-Content '%%v\src\main\resources\fabric.mod.json'"
    
    echo %%v updated!
)

echo All versions updated!
pause
