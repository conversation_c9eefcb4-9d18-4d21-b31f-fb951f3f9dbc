@echo off
if "%1"=="" (
    echo Usage: build_single_version.bat [version]
    echo Example: build_single_version.bat minecraft-1.20.4
    exit /b 1
)

set VERSION=%1
echo Building %VERSION%...

cd /d "%VERSION%"
if %ERRORLEVEL% NEQ 0 (
    echo Error: Directory %VERSION% not found!
    exit /b 1
)

call gradlew.bat clean build --no-daemon --console=plain

if %ERRORLEVEL% EQU 0 (
    echo %VERSION% - BUILD SUCCESSFUL!
    echo JAR files:
    dir "build\libs\*.jar" /b 2>nul
) else (
    echo %VERSION% - BUILD FAILED!
)

cd ..
pause
