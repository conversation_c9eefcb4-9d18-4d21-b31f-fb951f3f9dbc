package link.infra.indium.mixin;

import link.infra.indium.other.RenderDistanceEnhancer;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.block.entity.BlockEntityType;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.block.entity.BlockEntityRenderDispatcher;
import net.minecraft.client.util.math.MatrixStack;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(BlockEntityRenderDispatcher.class)
public class MixinEntityRenderDispatcher {

    @Inject(method = "render", at = @At("HEAD"), cancellable = true)
    private void onRender(BlockEntity blockEntity, float tickDelta, MatrixStack matrices, 
                         VertexConsumerProvider vertexConsumers, CallbackInfo ci) {
        
        if (!RenderDistanceEnhancer.isEnhancedMode()) return;
        
        // скрываем стойки и другие интерактивные блоки
        BlockEntityType<?> type = blockEntity.getType();
        if (type == BlockEntityType.CHEST ||
            type == BlockEntityType.TRAPPED_CHEST ||
            type == BlockEntityType.ENDER_CHEST ||
            type == BlockEntityType.FURNACE ||
            type == BlockEntityType.BLAST_FURNACE ||
            type == BlockEntityType.SMOKER ||
            type == BlockEntityType.ENCHANTING_TABLE ||
            type == BlockEntityType.BREWING_STAND ||
            type == BlockEntityType.HOPPER ||
            type == BlockEntityType.DROPPER ||
            type == BlockEntityType.DISPENSER ||
            type == BlockEntityType.JUKEBOX ||
            type == BlockEntityType.LECTERN ||
            type == BlockEntityType.BARREL ||
            type == BlockEntityType.BELL ||
            type == BlockEntityType.CAMPFIRE ||
            type == BlockEntityType.BEEHIVE ||
            type == BlockEntityType.CONDUIT ||
            type == BlockEntityType.BEACON ||
            type == BlockEntityType.DAYLIGHT_DETECTOR) {
            
            ci.cancel(); // не рендерим энтити
        }
    }
} 