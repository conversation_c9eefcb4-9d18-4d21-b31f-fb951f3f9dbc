plugins {
	id 'fabric-loom' version '1.10-SNAPSHOT'
	id 'maven-publish'

	id "com.github.breadmoirai.github-release" version "2.4.1"
	id "org.ajoberstar.grgit" version "4.1.1"
	id "com.modrinth.minotaur" version "2.+"
	id "com.matthewprenger.cursegradle" version "1.4.0"
}

String getGitVersion(Project project) {
	if (grgit != null) {
		var dirty = grgit.status().clean ? "" : "-dirty"
		// If we're going to create (or have created) a git tag, don't use dev version
		var willCreate = System.getenv("EXPECTED_VERSION") !== null && System.getenv("GITHUB_TOKEN")
		if (willCreate || grgit.describe(tags: true) == "${project.mod_version}+mc${project.minecraft_version}") {
			version = "${project.mod_version}+mc${project.minecraft_version}${dirty}"
		} else {
			version = "${project.mod_version}-dev.${grgit.head().abbreviatedId}+mc${project.minecraft_version}${dirty}"
		}
	} else {
		version = "${project.mod_version}-dev.unknown+mc${project.minecraft_version}"
	}
}

String getChangelog(String githubUrl) {
	// Get changes since the last tag
	return grgit.log(includes: ["HEAD"], excludes: [
			// Get the last tag, removing the number of commits since the tag and the current HEAD~ hash
			grgit.describe(commit: "HEAD~", tags: true).replaceAll("-\\d+-[a-z0-9]+\$", "")
	]).collect {
		"- ${it.shortMessage} (${it.author.name})"
	}.join("\n") + (githubUrl == null ? "" : "\n\nSee the full changes on Github: ${githubUrl}commits/${getGitVersion(project)}")
}

version = getGitVersion(project)
group = project.maven_group

base {
	archivesName = project.archives_base_name
}

repositories {
	maven {
		url = "https://api.modrinth.com/maven"
	}
}

dependencies {
    //to change the versions see the gradle.properties file
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

    // Fabric API - minimal for NoInteract functionality
    modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"

    // Sodium dependency commented out for NoInteract-only build
    // modImplementation "maven.modrinth:sodium:${project.sodium_version}"
}

processResources {
    inputs.property "version", project.version

    filesMatching("fabric.mod.json") {
        expand "version": project.version
    }
}

tasks.withType(JavaCompile).configureEach {
	// ensure that the encoding is set to UTF-8, no matter what the system default is
	// this fixes some edge cases with special characters not displaying correctly
	// see http://yodaconditions.net/blog/fix-for-java-file-encoding-problems-with-gradle.html
	// If Javadoc is generated, this must be specified in that task too.
	it.options.encoding = "UTF-8"

	// Minecraft 1.20.5 upwards uses Java 21.
	it.options.release = 21
}

java {
	// Loom will automatically attach sourcesJar to a RemapSourcesJar task and to the "build" task
	// if it is present.
	// If you remove this line, sources will not be generated.
	withSourcesJar()

	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

jar {
    from "LICENSE"
}

// configure the maven publication
publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }

    // select the repositories you want to publish to
    repositories {
        // uncomment to publish to the local maven
        // mavenLocal()
    }
}

// TODO: infer from fabric.mod.json?!
def supportedVersions = ["${project.minecraft_version}", "1.21.1"]
def verName = "Indium ${project.mod_version} for Minecraft 1.21.x/Sodium ${project.sodium_version}"

// Check version is as expected
if (System.getenv("EXPECTED_VERSION") !== null) {
	assert System.getenv("EXPECTED_VERSION") == project.version
}

publish {
	doFirst {
		// Require the user to "expect" a version to publish
		assert System.getenv("EXPECTED_VERSION") !== null : "EXPECTED_VERSION environment variable must be set to publish"
	}
}

if (System.getenv("GITHUB_TOKEN")) {
	assert System.getenv("GITHUB_REF_NAME")
	githubRelease {
		owner = System.getenv("GITHUB_REPOSITORY_OWNER")
		repo = System.getenv("GITHUB_REPOSITORY").split("/", 2)[1]
		tagName = project.version
		releaseName = verName
		targetCommitish = System.getenv("GITHUB_REF_NAME")
		draft = false
		body = getChangelog(null)
		token System.getenv("GITHUB_TOKEN")
		releaseAssets.from(remapJar)
	}

	publish.dependsOn(tasks.githubRelease)
}

if (System.getenv("MODRINTH_TOKEN")) {
	modrinth {
		projectId = project.archivesBaseName
		uploadFile = remapJar
		gameVersions = supportedVersions
		loaders = ["fabric", "quilt"]
		versionName = verName
		changelog = getChangelog(project.source_url)
		dependencies {
			required.version "sodium", "mc${project.sodium_minecraft_version}-${project.sodium_version}"
		}
	}

	publish.dependsOn(tasks.modrinth)
}

if (System.getenv("CURSEFORGE_TOKEN")) {
	curseforge {
		apiKey = System.getenv("CURSEFORGE_TOKEN")
		project {
			id = project.curseforge_id
			releaseType = "release"

			mainArtifact(remapJar) {
				displayName = verName
			}
			for (version in supportedVersions) {
				addGameVersion(version)
			}
			addGameVersion("Fabric")
			addGameVersion("Quilt")
			changelog = getChangelog(project.source_url)
			changelogType = "markdown"
			relations {
				requiredDependency "sodium"
			}
		}
	}

	publish.dependsOn(tasks.named("curseforge"))
}

