$versions = @("minecraft-1.21.4", "minecraft-1.21.5", "minecraft-1.21.6", "minecraft-1.21.7", "minecraft-1.21.8")

foreach ($version in $versions) {
    Write-Host "Fixing $version..."
    
    $buildGradlePath = "$version\build.gradle"
    $content = Get-Content $buildGradlePath -Raw
    
    # Replace the Fabric API dependencies
    $content = $content -replace '    // Fabric API\r?\n    modImplementation "net\.fabricmc\.fabric-api:fabric-renderer-api-v1:\$\{project\.fabric_version\}"\r?\n\tmodImplementation "net\.fabricmc\.fabric-api:fabric-resource-loader-v0:\$\{project\.fabric_version\}"\r?\n\r?\n    // For testing in dev environment\r?\n    modRuntimeOnly "net\.fabricmc\.fabric-api:fabric-api:\$\{project\.fabric_version\}"', '    // Fabric API' + "`r`n" + '    modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"'
    
    Set-Content $buildGradlePath $content
}

Write-Host "Done fixing all versions!"
