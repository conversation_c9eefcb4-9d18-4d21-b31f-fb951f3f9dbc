@echo off
echo Copying files to version directories...

set versions=minecraft-1.21 minecraft-1.21.1 minecraft-1.21.4 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8 minecraft-1.20.4 minecraft-1.20.1

for %%v in (%versions%) do (
    echo Copying to %%v...
    
    REM Create directories
    if not exist "%%v\src\main\java" mkdir "%%v\src\main\java"
    if not exist "%%v\src\main\resources" mkdir "%%v\src\main\resources"
    if not exist "%%v\gradle\wrapper" mkdir "%%v\gradle\wrapper"
    
    REM Copy source code
    xcopy "src\*" "%%v\src\" /E /I /Y
    
    REM Copy gradle files
    copy "build.gradle" "%%v\"
    copy "gradle.properties" "%%v\"
    copy "settings.gradle" "%%v\"
    copy "gradlew" "%%v\"
    copy "gradlew.bat" "%%v\"
    xcopy "gradle\*" "%%v\gradle\" /E /I /Y
    
    REM Copy other files
    copy "LICENSE" "%%v\"
    copy "README.md" "%%v\"
)

echo Done copying files to all version directories!
pause
