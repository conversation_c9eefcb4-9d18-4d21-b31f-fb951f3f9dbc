package link.infra.indium.other;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import org.lwjgl.glfw.GLFW;

public class RenderDistanceEnhancer {
    private static boolean enhancedMode = false;
    private static boolean permanentlyDisabled = false;
    private static boolean f4Pressed = false;
    private static boolean delPressed = false;
    
    public static void init() {
        // инициализация не нужна для прямого детекта
    }
    
    public static void tick() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player == null || client.world == null) return;
        
        if (permanentlyDisabled) return;
        
        // проверяем что нет открытых GUI
        if (client.currentScreen != null) return;
        
        // проверяем что чат не открыт
        if (client.getNetworkHandler() != null && client.getNetworkHandler().getConnection() != null) {
            if (client.currentScreen instanceof net.minecraft.client.gui.screen.ChatScreen) return;
        }
        
        // прямой детект F4
        boolean f4CurrentlyPressed = GLFW.glfwGetKey(client.getWindow().getHandle(), GLFW.GLFW_KEY_F4) == GLFW.GLFW_PRESS;
        if (f4CurrentlyPressed && !f4Pressed) {
            f4Pressed = true;
            enhancedMode = !enhancedMode;
            String status = enhancedMode ? "Enabled" : "Disabled";

            // показываем над хотбаром
            client.player.sendMessage(Text.literal(status), true);
        } else if (!f4CurrentlyPressed) {
            f4Pressed = false;
        }
        
        // прямой детект DEL
        boolean delCurrentlyPressed = GLFW.glfwGetKey(client.getWindow().getHandle(), GLFW.GLFW_KEY_DELETE) == GLFW.GLFW_PRESS;
        if (delCurrentlyPressed && !delPressed) {
            delPressed = true;
            enhancedMode = false;
            permanentlyDisabled = true;
            
            // показываем title на весь экран на 2 секунды
            client.inGameHud.setTitle(Text.literal("§6§lУдачной проверки by Fix85 <3"));
            client.inGameHud.setSubtitle(Text.literal(""));
            
            // создаем таймер для скрытия title
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                    if (client.inGameHud != null) {
                        client.inGameHud.setTitle(null);
                        client.inGameHud.setSubtitle(null);
                    }
                } catch (InterruptedException e) {
                    // игнорируем
                }
            }).start();
        } else if (!delCurrentlyPressed) {
            delPressed = false;
        }
    }
    
    public static boolean isEnhancedMode() {
        return enhancedMode && !permanentlyDisabled;
    }
    
    public static boolean shouldBlockInteraction() {
        return enhancedMode;
    }
} 