{"schemaVersion": 1, "id": "indium_enhanced", "version": "${version}", "name": "Render Optimizer", "description": "Performance optimization mod for better rendering", "authors": ["comp500"], "contact": {"sources": "https://github.com/comp500/Indium", "homepage": "https://modrinth.com/mod/indium", "issues": "https://github.com/comp500/Indium/issues"}, "license": "Apache-2.0", "icon": "assets/indium/icon.png", "environment": "client", "custom": {"fabric-renderer-api-v1:contains_renderer": true}, "entrypoints": {"client": ["link.infra.indium.PerformanceEnhancer"]}, "mixins": ["indium.mixins.json"], "depends": {"fabricloader": ">=0.8.0", "minecraft": "1.20.4", "fabric-api": "*"}}