@echo off
echo Creating NoInteract-only version...

REM Delete all renderer-related files
rmdir /s /q "src\main\java\link\infra\indium\renderer" 2>nul
rmdir /s /q "src\main\java\link\infra\indium\mixin\renderer" 2>nul
rmdir /s /q "src\main\java\link\infra\indium\mixin\sodium" 2>nul

REM Delete other unnecessary files
del "src\main\java\link\infra\indium\Indium.java" 2>nul
del "src\main\java\link\infra\indium\IndiumRenderer.java" 2>nul

REM Delete Fabric API stubs
rmdir /s /q "src\main\java\net" 2>nul

echo NoInteract-only version created!
pause
