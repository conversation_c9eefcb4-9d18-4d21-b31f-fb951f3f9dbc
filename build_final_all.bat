@echo off
echo Building all versions with correct dependencies...

set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.4 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

echo ========================================
echo Starting builds...
echo ========================================

for %%v in (%VERSIONS%) do (
    echo.
    echo Building %%v...
    cd /d "%%v"
    call gradlew.bat clean build --no-daemon --console=plain
    
    if %ERRORLEVEL% EQU 0 (
        echo [SUCCESS] %%v built successfully!
    ) else (
        echo [FAILED] %%v build failed!
    )
    
    cd ..
)

echo.
echo ========================================
echo Build Summary:
echo ========================================

for %%v in (%VERSIONS%) do (
    if exist "%%v\build\libs\*.jar" (
        echo [SUCCESS] %%v:
        for %%f in ("%%v\build\libs\*.jar") do echo   %%~nxf
    ) else (
        echo [FAILED] %%v: No JAR files found
    )
)

echo.
echo All builds completed!
pause
