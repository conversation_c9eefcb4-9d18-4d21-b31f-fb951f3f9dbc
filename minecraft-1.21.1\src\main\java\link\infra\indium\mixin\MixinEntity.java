package link.infra.indium.mixin;

import link.infra.indium.other.RenderDistanceEnhancer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(Entity.class)
public class MixinEntity {

    @Inject(method = "getBoundingBox", at = @At("HEAD"), cancellable = true)
    private void onGetBoundingBox(CallbackInfoReturnable<Box> cir) {
        // проверяем, что мы на клиенте и мод включен
        if (!RenderDistanceEnhancer.isEnhancedMode() || MinecraftClient.getInstance() == null) return;
        
        Entity entity = (Entity) (Object) this;
        EntityType<?> type = entity.getType();
        

        
        // уменьшаем hitbox для энтити (но не для игроков)
        if (type == EntityType.ITEM ||
            type == EntityType.EXPERIENCE_ORB ||
            type == EntityType.ARROW ||
            type == EntityType.SPECTRAL_ARROW ||
            type == EntityType.TRIDENT ||
            type == EntityType.SNOWBALL ||
            type == EntityType.EGG ||
            type == EntityType.ENDER_PEARL ||
            type == EntityType.EYE_OF_ENDER ||
            type == EntityType.POTION ||
            type == EntityType.EXPERIENCE_BOTTLE ||
            type == EntityType.FIREWORK_ROCKET ||
            type == EntityType.FIREBALL ||
            type == EntityType.SMALL_FIREBALL ||
            type == EntityType.DRAGON_FIREBALL ||
            type == EntityType.WITHER_SKULL ||
            type == EntityType.SHULKER_BULLET ||
            type == EntityType.FALLING_BLOCK ||
            type == EntityType.ITEM_FRAME ||
            type == EntityType.GLOW_ITEM_FRAME ||
            type == EntityType.PAINTING ||
            // type == EntityType.BOAT ||
            // type == EntityType.CHEST_BOAT ||
            type == EntityType.MINECART ||
            type == EntityType.CHEST_MINECART ||
            type == EntityType.FURNACE_MINECART ||
            type == EntityType.TNT_MINECART ||
            type == EntityType.HOPPER_MINECART ||
            type == EntityType.COMMAND_BLOCK_MINECART ||
            type == EntityType.SPAWNER_MINECART ||
            type == EntityType.END_CRYSTAL ||
            type == EntityType.ARMOR_STAND ||
            type == EntityType.LEASH_KNOT ||
            type == EntityType.AREA_EFFECT_CLOUD ||
            type == EntityType.EVOKER_FANGS ||
            type == EntityType.LIGHTNING_BOLT ||
            type == EntityType.MARKER ||
            type == EntityType.BLOCK_DISPLAY ||
            type == EntityType.ITEM_DISPLAY ||
            type == EntityType.TEXT_DISPLAY ||
            type == EntityType.INTERACTION) {
            
            // делаем hitbox очень маленьким, но не нулевым
            cir.setReturnValue(new Box(-0.001, -0.001, -0.001, 0.001, 0.001, 0.001));
        }
    }
} 