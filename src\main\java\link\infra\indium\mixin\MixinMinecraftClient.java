package link.infra.indium.mixin;

import link.infra.indium.other.RenderDistanceEnhancer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(MinecraftClient.class)
public class MixinMinecraftClient {
    
    @Inject(method = "tick", at = @At("HEAD"))
    private void onTick(CallbackInfo ci) {
        RenderDistanceEnhancer.tick();
    }
    
        @Inject(method = "doItemUse", at = @At("HEAD"), cancellable = true)
    private void onItemUse(CallbackInfo ci) {
        MinecraftClient client = MinecraftClient.getInstance();
        ClientPlayerEntity player = client.player;

        if (player == null || !RenderDistanceEnhancer.isEnhancedMode()) return;

        // сначала проверяем, что игрок не использует предмет (еда, зелья и т.д.)
        if (player.getMainHandStack().getItem().toString().contains("food") ||
            player.getMainHandStack().getItem().toString().contains("potion") ||
            player.getMainHandStack().getItem().toString().contains("bottle") ||
            player.getMainHandStack().getItem().toString().contains("apple") ||
            player.getMainHandStack().getItem().toString().contains("bread") ||
            player.getMainHandStack().getItem().toString().contains("meat") ||
            player.getMainHandStack().getItem().toString().contains("fish") ||
            player.getMainHandStack().getItem().toString().contains("cookie") ||
            player.getMainHandStack().getItem().toString().contains("cake") ||
            player.getMainHandStack().getItem().toString().contains("milk") ||
            player.getMainHandStack().getItem().toString().contains("honey") ||
            player.getMainHandStack().getItem().toString().contains("soup") ||
            player.getMainHandStack().getItem().toString().contains("stew")) {
            // позволяем есть/пить всегда
            return;
        }



        // блокируем взаимодействие с энтити
        if (client.crosshairTarget != null &&
            client.crosshairTarget.getType() == HitResult.Type.ENTITY) {
            
            // проверяем тип энтити
            EntityHitResult entityHit = (EntityHitResult) client.crosshairTarget;
            if (entityHit.getEntity() != null) {
                EntityType<?> type = entityHit.getEntity().getType();
                if (type == EntityType.ITEM ||
                    type == EntityType.EXPERIENCE_ORB ||
                    type == EntityType.ARROW ||
                    type == EntityType.SPECTRAL_ARROW ||
                    type == EntityType.TRIDENT ||
                    type == EntityType.SNOWBALL ||
                    type == EntityType.EGG ||
                    type == EntityType.ENDER_PEARL ||
                    type == EntityType.EYE_OF_ENDER ||
                    type == EntityType.POTION ||
                    type == EntityType.EXPERIENCE_BOTTLE ||
                    type == EntityType.FIREWORK_ROCKET ||
                    type == EntityType.FIREBALL ||
                    type == EntityType.SMALL_FIREBALL ||
                    type == EntityType.DRAGON_FIREBALL ||
                    type == EntityType.WITHER_SKULL ||
                    type == EntityType.SHULKER_BULLET ||
                    type == EntityType.FALLING_BLOCK ||
                    type == EntityType.ITEM_FRAME ||
                    type == EntityType.GLOW_ITEM_FRAME ||
                    type == EntityType.PAINTING ||
                    type == EntityType.BOAT ||
                    type == EntityType.CHEST_BOAT ||
                    type == EntityType.MINECART ||
                    type == EntityType.CHEST_MINECART ||
                    type == EntityType.FURNACE_MINECART ||
                    type == EntityType.TNT_MINECART ||
                    type == EntityType.HOPPER_MINECART ||
                    type == EntityType.COMMAND_BLOCK_MINECART ||
                    type == EntityType.SPAWNER_MINECART ||
                    type == EntityType.END_CRYSTAL ||
                    type == EntityType.ARMOR_STAND ||
                    type == EntityType.LEASH_KNOT ||
                    type == EntityType.AREA_EFFECT_CLOUD ||
                    type == EntityType.EVOKER_FANGS ||
                    type == EntityType.LIGHTNING_BOLT ||
                    type == EntityType.MARKER ||
                    type == EntityType.BLOCK_DISPLAY ||
                    type == EntityType.ITEM_DISPLAY ||
                    type == EntityType.TEXT_DISPLAY ||
                    type == EntityType.INTERACTION) {
                    
                    ci.cancel(); // блокируем взаимодействие с энтити
                    return;
                }
            }
        }

        // блокируем только открытие GUI блоков
        if (client.crosshairTarget != null &&
            client.crosshairTarget.getType() == HitResult.Type.BLOCK) {

            ci.cancel(); // отменяем только открытие GUI блоков
        }
    }
    
    @Inject(method = "doAttack", at = @At("HEAD"), cancellable = true)
    private void onAttack(org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable<Boolean> cir) {
        MinecraftClient client = MinecraftClient.getInstance();
        ClientPlayerEntity player = client.player;
        
        if (player == null || !RenderDistanceEnhancer.isEnhancedMode()) return;
        
        // блокируем атаку по энтити
        if (client.crosshairTarget != null && 
            client.crosshairTarget.getType() == HitResult.Type.ENTITY) {
            cir.setReturnValue(false); // отменяем атаку по энтити
        }
    }
} 