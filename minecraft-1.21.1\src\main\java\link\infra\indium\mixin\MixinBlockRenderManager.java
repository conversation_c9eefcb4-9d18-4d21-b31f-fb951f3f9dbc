package link.infra.indium.mixin;

import link.infra.indium.other.RenderDistanceEnhancer;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.block.BlockRenderManager;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.BlockRenderView;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(BlockRenderManager.class)
public class MixinBlockRenderManager {
    
    @Inject(method = "renderBlock", at = @At("HEAD"), cancellable = true)
    private void onRenderBlock(BlockState state, BlockPos pos, BlockRenderView world, MatrixStack matrices, 
                              net.minecraft.client.render.VertexConsumer vertexConsumer, boolean cull, net.minecraft.util.math.random.Random random, 
                              CallbackInfo ci) {
        
        if (!RenderDistanceEnhancer.isEnhancedMode()) return;
        
        Block block = state.getBlock();
        
        // скрываем визуально стойки, вагонетки и другие интерактивные блоки
        if (block == Blocks.CRAFTING_TABLE || 
            block == Blocks.CHEST || 
            block == Blocks.TRAPPED_CHEST ||
            block == Blocks.ENDER_CHEST ||
            block == Blocks.FURNACE ||
            block == Blocks.BLAST_FURNACE ||
            block == Blocks.SMOKER ||
            block == Blocks.ANVIL ||
            block == Blocks.CHIPPED_ANVIL ||
            block == Blocks.DAMAGED_ANVIL ||
            block == Blocks.ENCHANTING_TABLE ||
            block == Blocks.BREWING_STAND ||
            block == Blocks.CAULDRON ||
            block == Blocks.HOPPER ||
            block == Blocks.DROPPER ||
            block == Blocks.DISPENSER ||
            block == Blocks.OBSERVER ||
            block == Blocks.PISTON ||
            block == Blocks.STICKY_PISTON ||
            block == Blocks.NOTE_BLOCK ||
            block == Blocks.JUKEBOX ||
            block == Blocks.LECTERN ||
            block == Blocks.CARTOGRAPHY_TABLE ||
            block == Blocks.FLETCHING_TABLE ||
            block == Blocks.SMITHING_TABLE ||
            block == Blocks.GRINDSTONE ||
            block == Blocks.STONECUTTER ||
            block == Blocks.LOOM ||
            block == Blocks.COMPOSTER ||
            block == Blocks.BARREL ||
            block == Blocks.BELL ||
            block == Blocks.CAMPFIRE ||
            block == Blocks.SOUL_CAMPFIRE ||
            block == Blocks.BEEHIVE ||
            block == Blocks.BEE_NEST ||
            block == Blocks.RESPAWN_ANCHOR ||
            block == Blocks.LODESTONE ||
            block == Blocks.CONDUIT ||
            block == Blocks.BEACON ||
            block == Blocks.DAYLIGHT_DETECTOR ||
            block == Blocks.TARGET ||
            block == Blocks.CAKE ||
            block == Blocks.CANDLE_CAKE ||
            block == Blocks.WHITE_CANDLE_CAKE ||
            block == Blocks.ORANGE_CANDLE_CAKE ||
            block == Blocks.MAGENTA_CANDLE_CAKE ||
            block == Blocks.LIGHT_BLUE_CANDLE_CAKE ||
            block == Blocks.YELLOW_CANDLE_CAKE ||
            block == Blocks.LIME_CANDLE_CAKE ||
            block == Blocks.PINK_CANDLE_CAKE ||
            block == Blocks.GRAY_CANDLE_CAKE ||
            block == Blocks.LIGHT_GRAY_CANDLE_CAKE ||
            block == Blocks.CYAN_CANDLE_CAKE ||
            block == Blocks.PURPLE_CANDLE_CAKE ||
            block == Blocks.BLUE_CANDLE_CAKE ||
            block == Blocks.BROWN_CANDLE_CAKE ||
            block == Blocks.GREEN_CANDLE_CAKE ||
            block == Blocks.RED_CANDLE_CAKE ||
            block == Blocks.BLACK_CANDLE_CAKE) {
            
            ci.cancel(); // не рендерим блок
        }
    }
} 