@echo off
echo Creating Fabric API stubs for all versions...

set VERSIONS=minecraft-1.20.1 minecraft-1.20.4 minecraft-1.21 minecraft-1.21.1 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%VERSIONS%) do (
    echo Creating stubs for %%v...
    
    REM Create directories
    mkdir "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1\material" 2>nul
    mkdir "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1\mesh" 2>nul
    mkdir "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1" 2>nul
    
    REM Copy stubs from 1.21.4
    copy "minecraft-1.21.4\src\main\java\net\fabricmc\fabric\api\renderer\v1\material\*.java" "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1\material\" >nul 2>nul
    copy "minecraft-1.21.4\src\main\java\net\fabricmc\fabric\api\renderer\v1\mesh\*.java" "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1\mesh\" >nul 2>nul
    copy "minecraft-1.21.4\src\main\java\net\fabricmc\fabric\api\renderer\v1\RendererAccess.java" "%%v\src\main\java\net\fabricmc\fabric\api\renderer\v1\" >nul 2>nul
    
    echo %%v stubs created!
)

echo All stubs created!
pause
