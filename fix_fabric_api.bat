@echo off
echo Fixing Fabric API dependencies in all versions...

set versions=minecraft-1.21.1 minecraft-1.21.4 minecraft-1.21.5 minecraft-1.21.6 minecraft-1.21.7 minecraft-1.21.8

for %%v in (%versions%) do (
    echo Fixing %%v...
    powershell -Command "(Get-Content '%%v\build.gradle') -replace 'modImplementation\(fabricApi\.module\(\"fabric-renderer-api-v1\", project\.fabric_version\)\)', 'modImplementation \"net.fabricmc.fabric-api:fabric-renderer-api-v1:${project.fabric_version}\"' | Set-Content '%%v\build.gradle'"
    powershell -Command "(Get-Content '%%v\build.gradle') -replace 'modImplementation\(fabricApi\.module\(\"fabric-resource-loader-v0\", project\.fabric_version\)\)', 'modImplementation \"net.fabricmc.fabric-api:fabric-resource-loader-v0:${project.fabric_version}\"' | Set-Content '%%v\build.gradle'"
)

echo Done fixing all versions!
pause
