/*
 * Temporary compatibility stubs for Minecraft 1.21.4
 * These are placeholder implementations until proper Fabric API support is available
 */
package link.infra.indium.compat;

import net.minecraft.util.math.Direction;

// Temporary stubs for missing Fabric API classes
public class FabricApiStubs {
    
    // Stub for QuadView
    public interface QuadView {
        float x(int vertexIndex);
        float y(int vertexIndex);
        float z(int vertexIndex);
        int color(int vertexIndex);
        float u(int vertexIndex);
        float v(int vertexIndex);
        int lightmap(int vertexIndex);
        boolean hasNormal(int vertexIndex);
        float normalX(int vertexIndex);
        float normalY(int vertexIndex);
        float normalZ(int vertexIndex);
        Direction cullFace();
        Direction nominalFace();
        Direction lightFace();
    }
    
    // Stub for QuadEmitter
    public interface QuadEmitter extends QuadView {
        QuadEmitter pos(int vertexIndex, float x, float y, float z);
        QuadEmitter color(int vertexIndex, int color);
        QuadEmitter uv(int vertexIndex, float u, float v);
        QuadEmitter lightmap(int vertexIndex, int lightmap);
        QuadEmitter normal(int vertexIndex, float x, float y, float z);
        QuadEmitter cullFace(Direction face);
        QuadEmitter nominalFace(Direction face);
        QuadEmitter emit();
    }
    
    // Stub for RenderMaterial
    public interface RenderMaterial {
        BlendMode blendMode();
        boolean disableColorIndex();
        boolean disableDiffuse();
        boolean disableAo();
        boolean emissive();
    }
    
    // Stub for BlendMode
    public enum BlendMode {
        DEFAULT,
        SOLID,
        CUTOUT,
        CUTOUT_MIPPED,
        TRANSLUCENT
    }
    
    // Stub for RenderContext
    public interface RenderContext {
        QuadEmitter getEmitter();
        void pushTransform(QuadTransform transform);
        void popTransform();
    }
    
    // Stub for QuadTransform
    public interface QuadTransform {
        boolean transform(QuadView quad);
    }
    
    // Stub for Mesh
    public interface Mesh {
        void outputTo(QuadEmitter emitter);
    }
}
